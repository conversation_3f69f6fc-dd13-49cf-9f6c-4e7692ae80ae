import pandas as pd
import numpy as np
from datetime import datetime
import os
import glob
import zipfile
import tempfile
import shutil
from pathlib import Path
import re
import difflib

def normalize_timestamp(timestamp_series):
    """Normalize timestamps to improve matching accuracy."""
    if timestamp_series.empty:
        return timestamp_series
    
    # Convert to datetime and floor to seconds to remove milliseconds
    normalized = pd.to_datetime(timestamp_series, errors='coerce')
    normalized = normalized.dt.floor('s')  # Remove milliseconds
    
    # Handle timezone issues by converting to UTC if timezone-aware
    if normalized.dt.tz is not None:
        normalized = normalized.dt.tz_convert('UTC').dt.tz_localize(None)
    
    return normalized

def normalize_athlete_name(name):
    """Normalize athlete names for consistent matching."""
    if pd.isna(name) or name == "Unknown Athlete":
        return name
    return str(name).strip().title()

def unify_athlete_names(raw_names, cutoff=0.9):
    """
    Unify athlete names by normalizing and fuzzy-clustering near-duplicates.
    
    Args:
        raw_names: List of raw athlete names
        cutoff: Similarity cutoff for fuzzy matching (0.9 = 90% similar)
    
    Returns:
        tuple: (canonical_names_list, raw_to_canonical_mapping)
    """
    # First normalize all names
    normalized_names = []
    raw_to_normalized = {}
    
    for raw_name in raw_names:
        if raw_name and raw_name != "Unknown Athlete":
            normalized = normalize_athlete_name(raw_name)
            normalized_names.append(normalized)
            raw_to_normalized[raw_name] = normalized
        else:
            raw_to_normalized[raw_name] = raw_name
    
    # Remove duplicates while preserving order
    unique_normalized = []
    seen = set()
    for name in normalized_names:
        if name not in seen:
            unique_normalized.append(name)
            seen.add(name)
    
    # Fuzzy cluster similar names
    canonical_names = []
    raw_to_canonical = {}
    
    for name in unique_normalized:
        # Check if this name is similar to any existing canonical name
        close_matches = difflib.get_close_matches(name, canonical_names, n=1, cutoff=cutoff)
        
        if close_matches:
            # Map to existing canonical name
            canonical = close_matches[0]
            print(f"🔀 Mapping '{name}' → '{canonical}'")
        else:
            # This becomes a new canonical name
            canonical = name
            canonical_names.append(canonical)
        
        # Update mapping for all raw names that normalized to this name
        for raw_name, normalized in raw_to_normalized.items():
            if normalized == name:
                raw_to_canonical[raw_name] = canonical
    
    return canonical_names, raw_to_canonical

def extract_athlete_from_filename(filename):
    """Extract athlete name from filename."""
    # Remove file extension
    name = os.path.splitext(filename)[0]
    
    # Remove common prefixes/suffixes
    prefixes_to_remove = ['Metrics_', 'metrics_', 'data_', 'Data_']
    for prefix in prefixes_to_remove:
        if name.startswith(prefix):
            name = name[len(prefix):]
    
    # Remove numbers and common suffixes like (1), (2), etc.
    name = re.sub(r'\s*\(\d+\)$', '', name)
    name = re.sub(r'_\d+$', '', name)
    
    # Clean up the name
    name = name.replace('_', ' ').strip()
    
    if name and name != "Unknown Athlete":
        return normalize_athlete_name(name)
    return "Unknown Athlete"

# Column mapping for HitTrax data
HITTRAX_COLUMN_MAPPING = {
    'Exit Velocity': 'Exit_Velocity',
    'Launch Angle': 'Launch_Angle', 
    'Direction': 'Direction',
    'Distance': 'Distance',
    'Hang Time': 'Hang_Time',
    'Height': 'Height',
    'Result': 'Result',
    'Spin Rate': 'Spin_Rate',
    'Date/Time': 'Date_Time',
    'Batter Name': 'Batter_Name',
    'Pitcher Name': 'Pitcher_Name',
    'Pitch Velocity': 'Pitch_Velocity',
    'Pitch Type': 'Pitch_Type'
}

def parse_blast_data_complete(file_path, athlete_name=None):
    """Parse Blast data with complete column preservation."""
    try:
        df = pd.read_csv(file_path)
        
        if df.empty:
            return pd.DataFrame()
        
        # Normalize timestamp
        if 'Date' in df.columns:
            df['Date'] = normalize_timestamp(df['Date'])
        
        # Add athlete name
        if athlete_name:
            df['athlete'] = normalize_athlete_name(athlete_name)
        elif 'athlete' not in df.columns:
            df['athlete'] = "Unknown Athlete"
        
        # Add source identifier
        df['data_source'] = 'Blast'
        
        return df
        
    except Exception as e:
        print(f"❌ Error reading Blast file {file_path}: {e}")
        return pd.DataFrame()

def parse_hittrax_data_complete(file_path, athlete_name=None):
    """Parse HitTrax data with complete column preservation and mapping."""
    try:
        df = pd.read_csv(file_path)
        
        if df.empty:
            return pd.DataFrame()
        
        # APPLY THE COLUMN MAPPING
        df = df.rename(columns=HITTRAX_COLUMN_MAPPING)
        
        # Find date column (could be various names after mapping)
        date_col = None
        for col in ['Date_Time', 'Date/Time', 'Date', 'Time']:
            if col in df.columns:
                date_col = col
                break
        
        if date_col:
            df['Date'] = normalize_timestamp(df[date_col])
        
        # Find user/athlete column
        user_col = None
        for col in ['Batter_Name', 'Batter Name', 'User', 'Athlete', 'Player']:
            if col in df.columns:
                user_col = col
                break
        
        if user_col and athlete_name is None:
            # Extract athlete name from the data
            unique_users = df[user_col].dropna().apply(normalize_athlete_name).unique()
            if len(unique_users) == 1:
                athlete_name = unique_users[0]
        
        # Add athlete name
        if athlete_name:
            df['athlete'] = normalize_athlete_name(athlete_name)
        elif 'athlete' not in df.columns:
            df['athlete'] = "Unknown Athlete"
        
        # Add ball contact indicator
        df['has_contact'] = True  # HitTrax only records actual hits
        
        # Add source identifier
        df['data_source'] = 'HitTrax'
        
        return df
        
    except Exception as e:
        print(f"❌ Error reading HitTrax file {file_path}: {e}")
        return pd.DataFrame()

def find_best_matches_complete(blast_df, hittrax_df, max_time_diff=10):
    """Find best matches between Blast and HitTrax data with debugging."""
    matches = []
    used_blast_indices = set()
    used_hittrax_indices = set()
    time_differences = []  # Track time differences for debugging
    
    if blast_df.empty or hittrax_df.empty:
        return matches, used_blast_indices, used_hittrax_indices
    
    # Only match records with ball contact
    hittrax_contact = hittrax_df[hittrax_df['has_contact'] == True].copy()
    
    for blast_idx, blast_row in blast_df.iterrows():
        if blast_idx in used_blast_indices:
            continue
            
        blast_time = blast_row['Date']
        if pd.isna(blast_time):
            continue
            
        best_match = None
        best_time_diff = float('inf')
        
        for hittrax_idx, hittrax_row in hittrax_contact.iterrows():
            if hittrax_idx in used_hittrax_indices:
                continue
                
            hittrax_time = hittrax_row['Date']
            if pd.isna(hittrax_time):
                continue
            
            # Calculate time difference in seconds
            time_diff = abs((blast_time - hittrax_time).total_seconds())
            
            if time_diff <= max_time_diff and time_diff < best_time_diff:
                best_match = hittrax_idx
                best_time_diff = time_diff
        
        if best_match is not None:
            confidence = 'High' if best_time_diff <= 2 else 'Medium'
            matches.append({
                'blast_idx': blast_idx,
                'hittrax_idx': best_match,
                'time_diff': best_time_diff,
                'confidence': confidence
            })
            used_blast_indices.add(blast_idx)
            used_hittrax_indices.add(best_match)
            time_differences.append(best_time_diff)
    
    # Print debugging statistics
    if time_differences:
        print(f"   📊 Time differences: min={min(time_differences):.1f}s, mean={np.mean(time_differences):.1f}s, max={max(time_differences):.1f}s")
    
    return matches, used_blast_indices, used_hittrax_indices

def create_complete_merged_dataset(blast_df, hittrax_df, hittrax_columns, matches, used_blast_indices, used_hittrax_indices):
    """Create merged dataset with ALL columns from both sources."""
    merged_records = []

    # Create matched records
    for match in matches:
        blast_row = blast_df.iloc[match['blast_idx']].copy()
        hittrax_row = hittrax_df.iloc[match['hittrax_idx']].copy()

        # Create merged record
        merged_record = {}

        # Add Blast data with prefix
        for col in blast_df.columns:
            if col not in ['athlete', 'data_source']:
                merged_record[f'Blast_{col}'] = blast_row[col]

        # Add HitTrax data with prefix
        for col in hittrax_df.columns:
            if col not in ['athlete', 'data_source']:
                merged_record[f'HitTrax_{col}'] = hittrax_row[col]

        # Add match metadata
        merged_record['Athlete'] = blast_row['athlete']
        merged_record['Match_Type'] = 'Matched'
        merged_record['Confidence'] = match['confidence']
        merged_record['Time_Difference_Seconds'] = match['time_diff']

        merged_records.append(merged_record)

    # Add unmatched Blast records
    for idx, row in blast_df.iterrows():
        if idx not in used_blast_indices:
            merged_record = {}

            # Add Blast data
            for col in blast_df.columns:
                if col not in ['athlete', 'data_source']:
                    merged_record[f'Blast_{col}'] = row[col]

            # Add empty HitTrax columns using actual dataframe columns
            for col in hittrax_df.columns:
                if col not in ['athlete', 'data_source']:
                    merged_record[f'HitTrax_{col}'] = None

            # Add metadata
            merged_record['Athlete'] = row['athlete']
            merged_record['Match_Type'] = 'Blast_Only'
            merged_record['Confidence'] = None
            merged_record['Time_Difference_Seconds'] = None

            merged_records.append(merged_record)

    # Add unmatched HitTrax records
    for idx, row in hittrax_df.iterrows():
        if idx not in used_hittrax_indices:
            merged_record = {}

            # Add empty Blast columns using actual dataframe columns
            for col in blast_df.columns:
                if col not in ['athlete', 'data_source']:
                    merged_record[f'Blast_{col}'] = None

            # Add HitTrax data
            for col in hittrax_df.columns:
                if col not in ['athlete', 'data_source']:
                    merged_record[f'HitTrax_{col}'] = row[col]

            # Add metadata
            merged_record['Athlete'] = row['athlete']
            merged_record['Match_Type'] = 'HitTrax_Only'
            merged_record['Confidence'] = None
            merged_record['Time_Difference_Seconds'] = None

            merged_records.append(merged_record)

    return pd.DataFrame(merged_records)

def deduplicate_merged_data(df, context="data"):
    """IMPROVED: Deduplicate merged data using intelligent column selection."""
    if df.empty:
        return df

    original_count = len(df)

    # Core columns that should always be used for deduplication
    dedup_columns = ['Athlete', 'Match_Type']

    # Add confidence for matched records
    if 'Confidence' in df.columns:
        dedup_columns.append('Confidence')

    # Add Time_Difference_Seconds for matched records with specific values
    if 'Time_Difference_Seconds' in df.columns:
        matched_with_time = df[(df['Match_Type'] == 'Matched') & df['Time_Difference_Seconds'].notna()]
        if not matched_with_time.empty:
            dedup_columns.append('Time_Difference_Seconds')

    # Add key performance metrics for more precise deduplication
    performance_metrics = []
    for col in df.columns:
        if any(metric in col for metric in ['Bat_Speed', 'Exit_Velocity', 'Launch_Angle', 'Velo', 'Distance']):
            performance_metrics.append(col)

    # Limit performance metrics to avoid over-specification (use top 3)
    dedup_columns.extend(performance_metrics[:3])

    # Add timestamp-related columns if they exist
    timestamp_cols = []
    for col in df.columns:
        if any(indicator in col for indicator in ['Date', 'Time']) and any(source in col for source in ['Blast', 'HitTrax']):
            timestamp_cols.append(col)

    # Add up to 2 timestamp columns
    dedup_columns.extend(timestamp_cols[:2])

    # Only use columns that actually exist in the DataFrame
    dedup_columns = [col for col in dedup_columns if col in df.columns]

    # Remove duplicates
    if dedup_columns:
        deduplicated_df = df.drop_duplicates(subset=dedup_columns, keep='first')
        removed_count = original_count - len(deduplicated_df)

        if removed_count > 0:
            print(f"   🧹 {context}: Removed {removed_count} duplicates ({original_count} → {len(deduplicated_df)} records)")
            print(f"   📋 Dedup columns used: {', '.join(dedup_columns[:5])}{'...' if len(dedup_columns) > 5 else ''}")
        else:
            print(f"   ✅ {context}: No duplicates found ({len(deduplicated_df)} records)")

        return deduplicated_df
    else:
        print(f"   ⚠️ {context}: No suitable columns for deduplication")
        return df

def merge_single_athlete(blast_file, hittrax_file, athlete_name=None, max_time_diff=10):
    """Merge data for a single athlete with complete columns."""
    print(f"\n🔄 Processing athlete data...")

    # Handle cases where one file type might be missing
    if blast_file is None and hittrax_file is None:
        print("   ❌ No files provided")
        return None

    # Load data
    if blast_file:
        blast_df = parse_blast_data_complete(blast_file, athlete_name)
        print(f"   📊 Loaded {len(blast_df)} Blast records from {os.path.basename(blast_file)}")
    else:
        blast_df = pd.DataFrame()
        print("   📊 No Blast data")

    if hittrax_file:
        hittrax_df = parse_hittrax_data_complete(hittrax_file, athlete_name)
        hittrax_columns = list(hittrax_df.columns)
        print(f"   🎯 Loaded {len(hittrax_df)} HitTrax records from {os.path.basename(hittrax_file)}")
    else:
        hittrax_df = pd.DataFrame()
        hittrax_columns = []
        print("   🎯 No HitTrax data")

    if blast_df.empty and hittrax_df.empty:
        print("   ❌ No valid data found in either file")
        return None

    print(f"   📈 Loaded {len(blast_df)} Blast records, {len(hittrax_df)} HitTrax records")
    print(f"   🎯 {hittrax_df['has_contact'].sum()} HitTrax records with ball contact")

    # Find matches
    matches, used_blast_indices, used_hittrax_indices = find_best_matches_complete(blast_df, hittrax_df, max_time_diff)

    # Create complete merged dataset
    merged_df = create_complete_merged_dataset(blast_df, hittrax_df, hittrax_columns, matches, used_blast_indices, used_hittrax_indices)

    high_confidence = sum(1 for m in matches if m['confidence'] == 'High')
    medium_confidence = sum(1 for m in matches if m['confidence'] == 'Medium')

    print(f"   ✅ Found {len(matches)} matches ({high_confidence} high confidence, {medium_confidence} medium confidence)")
    print(f"   📋 Created {len(merged_df)} total records")

    # Deduplicate
    merged_df = deduplicate_merged_data(merged_df, "merged data")

    return merged_df

def extract_athlete_from_hittrax_file(file_path):
    """Extract athlete name from HitTrax file by reading the data."""
    try:
        df = pd.read_csv(file_path, nrows=10)  # Read just a few rows to get athlete name

        # Apply column mapping first
        df = df.rename(columns=HITTRAX_COLUMN_MAPPING)

        # Look for athlete/user column
        user_col = None
        for col in ['Batter_Name', 'Batter Name', 'User', 'Athlete', 'Player']:
            if col in df.columns:
                user_col = col
                break

        if user_col:
            # Get the most common non-null athlete name
            athletes = df[user_col].dropna().apply(normalize_athlete_name)
            if not athletes.empty:
                return athletes.mode().iloc[0] if len(athletes.mode()) > 0 else athletes.iloc[0]

        # Fallback to filename
        return extract_athlete_from_filename(os.path.basename(file_path))

    except Exception as e:
        print(f"   ⚠️ Could not extract athlete from {file_path}: {e}")
        return extract_athlete_from_filename(os.path.basename(file_path))

def find_hittrax_files(folder_path):
    """Find HitTrax CSV files in the folder."""
    hittrax_files = []

    # Pattern 1: data (1).csv, data (2).csv, etc.
    pattern1 = os.path.join(folder_path, "data (*).csv")
    files1 = glob.glob(pattern1)

    # Pattern 2: Any CSV that looks like HitTrax data
    pattern2 = os.path.join(folder_path, "*.csv")
    all_csv_files = glob.glob(pattern2)

    for file in all_csv_files:
        if file not in hittrax_files:
            try:
                # Quick check if this looks like HitTrax data
                df_sample = pd.read_csv(file, nrows=5)
                hittrax_indicators = ['Exit Velocity', 'Launch Angle', 'Direction', 'Distance', 'Result']

                if any(indicator in df_sample.columns for indicator in hittrax_indicators):
                    hittrax_files.append(file)

            except Exception:
                continue

    return sorted(hittrax_files)

def find_blast_files(folder_path):
    """Find Blast CSV files in the folder."""
    blast_files = []

    # Pattern 1: Metrics files
    pattern1 = os.path.join(folder_path, "Metrics*.csv")
    files1 = glob.glob(pattern1)

    # Pattern 2: Any CSV that looks like Blast data
    pattern2 = os.path.join(folder_path, "*.csv")
    all_csv_files = glob.glob(pattern2)

    for file in all_csv_files:
        if file not in blast_files:
            try:
                # Quick check if this looks like Blast data
                df_sample = pd.read_csv(file, nrows=5)
                blast_indicators = ['Bat Speed', 'Attack Angle', 'Vertical Bat Angle', 'Time to Contact']

                if any(indicator in df_sample.columns for indicator in blast_indicators):
                    blast_files.append(file)

            except Exception:
                continue

    return sorted(blast_files)

def extract_athletes_from_files(hittrax_files, blast_files):
    """Extract unique athlete names from all files."""
    athletes = set()

    # Extract from HitTrax files
    for file in hittrax_files:
        athlete = extract_athlete_from_hittrax_file(file)
        if athlete and athlete != "Unknown Athlete":
            athletes.add(athlete)

    # Extract from Blast files
    for file in blast_files:
        athlete = extract_athlete_from_filename(os.path.basename(file))
        if athlete and athlete != "Unknown Athlete":
            athletes.add(athlete)

    return sorted(list(athletes))

def match_files_by_athlete(hittrax_files, blast_files, athletes, name_mapping=None):
    """Create athlete-to-files mapping using canonical names"""
    athlete_files = {}

    for athlete in athletes:
        athlete_files[athlete] = {
            'hittrax_files': [],
            'blast_files': []
        }

        # Find HitTrax files for this athlete
        for file in hittrax_files:
            file_athlete = extract_athlete_from_hittrax_file(file)
            if file_athlete:
                # Use canonical mapping if available
                canonical_athlete = name_mapping.get(file_athlete, file_athlete) if name_mapping else file_athlete
                if canonical_athlete == athlete:
                    athlete_files[athlete]['hittrax_files'].append(file)

        # Find Blast files for this athlete
        for file in blast_files:
            file_athlete = extract_athlete_from_filename(os.path.basename(file))
            if file_athlete:
                # Use canonical mapping if available
                canonical_athlete = name_mapping.get(file_athlete, file_athlete) if name_mapping else file_athlete
                if canonical_athlete == athlete:
                    athlete_files[athlete]['blast_files'].append(file)

    return athlete_files

def batch_process_athletes(input_folder, output_folder=None, max_time_diff=10):
    """
    Main batch processing function

    Args:
        input_folder: Folder containing CSV files
        output_folder: Where to save results (optional)
        max_time_diff: Maximum time difference for matching (seconds)
    """
    print(f"🚀 Starting batch processing...")
    print(f"📁 Input folder: {input_folder}")

    if output_folder is None:
        output_folder = os.path.join(input_folder, "merged_results")

    os.makedirs(output_folder, exist_ok=True)
    print(f"💾 Output folder: {output_folder}")

    # Process regular folder
    hittrax_files = find_hittrax_files(input_folder)
    blast_files = find_blast_files(input_folder)

    print(f"📊 Found {len(hittrax_files)} HitTrax files")
    print(f"🎯 Found {len(blast_files)} Blast files")

    if len(hittrax_files) == 0 and len(blast_files) == 0:
        print("❌ No CSV files found in the input folder!")
        return []

    # Extract athlete names
    raw_athletes = extract_athletes_from_files(hittrax_files, blast_files)
    print(f"👥 Detected {len(raw_athletes)} raw athlete names: {', '.join(raw_athletes)}")

    # Unify athlete names (handle typos and case differences)
    print(f"\n🔀 Unifying athlete names...")
    canonical_athletes, name_mapping = unify_athlete_names(raw_athletes, cutoff=0.9)
    print(f"✅ Unified to {len(canonical_athletes)} canonical athletes: {', '.join(canonical_athletes)}")

    # Match files to athletes using canonical names
    athlete_files = match_files_by_athlete(hittrax_files, blast_files, canonical_athletes, name_mapping)

    # Process each athlete
    all_merged_data = []
    processing_summary = []

    for athlete in canonical_athletes:
        print(f"\n🏃 Processing {athlete}...")

        athlete_data = athlete_files[athlete]
        hittrax_count = len(athlete_data['hittrax_files'])
        blast_count = len(athlete_data['blast_files'])

        print(f"   📊 Found {hittrax_count} HitTrax files, {blast_count} Blast files")

        athlete_merged_data = []

        # Process combinations of files
        if hittrax_count > 0 and blast_count > 0:
            print(f"   🔄 Processing {hittrax_count} HitTrax × {blast_count} Blast file combinations...")

            for hittrax_file in athlete_data['hittrax_files']:
                for blast_file in athlete_data['blast_files']:
                    merged_data = merge_single_athlete(blast_file, hittrax_file, athlete, max_time_diff)
                    if merged_data is not None:
                        athlete_merged_data.append(merged_data)

        # Handle cases where we only have one type of data
        elif hittrax_count > 0:
            print(f"   📊 Processing {hittrax_count} HitTrax files (no Blast data)")
            # Process HitTrax-only data
            for hittrax_file in athlete_data['hittrax_files']:
                # Create empty blast data for consistency
                empty_blast_file = None  # This will be handled in merge_single_athlete
                merged_data = merge_single_athlete(empty_blast_file, hittrax_file, athlete, max_time_diff)
                if merged_data is not None:
                    athlete_merged_data.append(merged_data)

        elif blast_count > 0:
            print(f"   🎯 Processing {blast_count} Blast files (no HitTrax data)")
            # Process Blast-only data
            for blast_file in athlete_data['blast_files']:
                # Create empty hittrax data for consistency
                empty_hittrax_file = None  # This will be handled in merge_single_athlete
                merged_data = merge_single_athlete(blast_file, empty_hittrax_file, athlete, max_time_diff)
                if merged_data is not None:
                    athlete_merged_data.append(merged_data)

        # Combine all data for this athlete
        if athlete_merged_data:
            combined_athlete_data = pd.concat(athlete_merged_data, ignore_index=True)

            # Final deduplication for this athlete
            combined_athlete_data = deduplicate_merged_data(combined_athlete_data, f"{athlete} final data")

            # Save individual athlete file
            safe_athlete_name = athlete.replace(' ', '_').replace('/', '_')
            athlete_output_file = os.path.join(output_folder, f"{safe_athlete_name}_complete_data.csv")
            combined_athlete_data.to_csv(athlete_output_file, index=False)

            print(f"   💾 Saved {len(combined_athlete_data)} records to {athlete_output_file}")

            all_merged_data.append(combined_athlete_data)

            # Track summary
            matched_count = len(combined_athlete_data[combined_athlete_data['Match_Type'] == 'Matched'])
            blast_only_count = len(combined_athlete_data[combined_athlete_data['Match_Type'] == 'Blast_Only'])
            hittrax_only_count = len(combined_athlete_data[combined_athlete_data['Match_Type'] == 'HitTrax_Only'])

            processing_summary.append({
                'Athlete': athlete,
                'Total_Records': len(combined_athlete_data),
                'Matched': matched_count,
                'Blast_Only': blast_only_count,
                'HitTrax_Only': hittrax_only_count,
                'HitTrax_Files': hittrax_count,
                'Blast_Files': blast_count
            })
        else:
            print(f"   ❌ No data processed for {athlete}")

    # Combine all athlete data
    if all_merged_data:
        final_combined_data = pd.concat(all_merged_data, ignore_index=True)

        # Final global deduplication
        final_combined_data = deduplicate_merged_data(final_combined_data, "final combined data")

        # Save combined file
        combined_output_file = os.path.join(output_folder, "ALL_ATHLETES_complete_data.csv")
        final_combined_data.to_csv(combined_output_file, index=False)

        print(f"\n💾 Saved combined data: {len(final_combined_data)} total records to {combined_output_file}")

        # Print summary
        print(f"\n📊 Processing Summary:")
        print(f"{'Athlete':<20} {'Total':<8} {'Matched':<8} {'Blast':<8} {'HitTrax':<8} {'Files':<10}")
        print("-" * 70)

        for summary in processing_summary:
            files_info = f"{summary['Blast_Files']}B+{summary['HitTrax_Files']}H"
            print(f"{summary['Athlete']:<20} {summary['Total_Records']:<8} {summary['Matched']:<8} {summary['Blast_Only']:<8} {summary['HitTrax_Only']:<8} {files_info:<10}")

        total_records = sum(s['Total_Records'] for s in processing_summary)
        total_matched = sum(s['Matched'] for s in processing_summary)
        total_blast_only = sum(s['Blast_Only'] for s in processing_summary)
        total_hittrax_only = sum(s['HitTrax_Only'] for s in processing_summary)

        print("-" * 70)
        print(f"{'TOTAL':<20} {total_records:<8} {total_matched:<8} {total_blast_only:<8} {total_hittrax_only:<8}")

        match_rate = (total_matched / total_records * 100) if total_records > 0 else 0
        print(f"\n🎯 Overall match rate: {match_rate:.1f}%")

        return final_combined_data
    else:
        print("❌ No data was successfully processed!")
        return pd.DataFrame()

# Example usage scenarios
if __name__ == "__main__":
    input_folder = "C:/Users/<USER>/OneDrive/Desktop/BlastHittrax_MultiAthlete"
    batch_process_athletes(input_folder, max_time_diff=10)
